2025-08-23 01:51:55.514 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化代理服务器V2 - 组件化架构
2025-08-23 01:51:55.521 [main] WARN  c.x.p.s.c.ProxyServerV2ConfigManager - 配置目录中未找到YAML配置文件: /app/config/
2025-08-23 01:51:55.522 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 使用properties文件指定的配置文件: netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml
2025-08-23 01:51:55.630 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 成功加载外部 YAML 配置文件: C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\configs\development\server\proxy-server-v2.yml
2025-08-23 01:51:55.631 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 配置加载完成 - 认证: 启用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
2025-08-23 01:51:55.631 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 认证配置 - 用户名: admin, 超时时间: 30秒
2025-08-23 01:51:55.631 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 连接池配置 - 最大连接数/主机: 20, 空闲超时: 30秒, 清理间隔: 30秒
2025-08-23 01:51:55.631 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 性能监控配置 - 报告间隔: 300秒
2025-08-23 01:51:55.631 [main] INFO  c.x.p.s.c.ProxyServerV2ConfigManager - 黑名单配置 - 失败阈值: 3, 缓存超时: 180秒
2025-08-23 01:51:55.638 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
2025-08-23 01:51:55.639 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=48, queueCapacity=5000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
2025-08-23 01:51:55.641 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2核心组件初始化完成
2025-08-23 01:51:55.641 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动代理服务器V2...
2025-08-23 01:51:55.646 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 初始化系统组件...
2025-08-23 01:51:55.647 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 系统组件初始化完成
2025-08-23 01:51:55.647 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置路由规则...
2025-08-23 01:51:55.648 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=tcp-direct, name=tcp直连, priority=10, outbound=tcpDirect, enabled=true, matchers=1}
2025-08-23 01:51:55.648 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.router.DefaultRouter - 添加路由规则: RouteRule{id=udp-direct, name=udp直连, priority=10, outbound=udpDirect, enabled=true, matchers=1}
2025-08-23 01:51:55.649 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 路由规则配置完成，共配置2条规则
2025-08-23 01:51:55.649 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册Outbound处理器...
2025-08-23 01:51:55.793 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.o.i.AsyncTcpDirectOutboundHandler - 异步TCP直连出站处理器初始化完成: tcpDirect
2025-08-23 01:51:55.793 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 注册Outbound处理器: tcpDirect (AsyncTcpDirect)
2025-08-23 01:51:55.794 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册TCP直连Outbound: tcp直连 (tcpDirect)
2025-08-23 01:51:55.797 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 注册Outbound处理器: udpDirect (udp_direct)
2025-08-23 01:51:55.797 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册UDP直连Outbound: udp直连 (udpDirect)
2025-08-23 01:51:55.797 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Outbound处理器注册完成
2025-08-23 01:51:55.798 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 使用异步连接管理，无需启动传统连接池
2025-08-23 01:51:55.798 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
2025-08-23 01:51:55.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
2025-08-23 01:51:55.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
2025-08-23 01:51:55.811 [ForkJoinPool.commonPool-worker-1] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 48
2025-08-23 01:51:55.811 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 配置Inbound服务器...
2025-08-23 01:51:55.816 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 注册Inbound服务器: main-multiplex (0.0.0.0:8888)
2025-08-23 01:51:55.816 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 注册多路复用Inbound: 主多路复用服务器 (main-multiplex) - 端口: 8888
2025-08-23 01:51:55.816 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器配置完成
2025-08-23 01:51:55.816 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动所有Inbound服务器...
2025-08-23 01:51:55.816 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.inbound.InboundServerManager - 启动所有Inbound服务器，共1个
2025-08-23 01:51:55.818 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.i.AbstractInboundServer - 启动Inbound服务器: main-multiplex (0.0.0.0:8888)
2025-08-23 01:51:55.821 [ForkJoinPool.commonPool-worker-1] WARN  c.x.p.s.c.ProxyServerConfigManager - 配置目录中未找到YAML配置文件: /app/config/
2025-08-23 01:51:55.821 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.c.ProxyServerConfigManager - 使用properties文件指定的配置文件: netty_multiplex_proxy/configs/development/server/proxy-server-v2.yml
2025-08-23 01:51:55.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.c.ProxyServerConfigManager - 成功加载外部 YAML 配置文件: C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\configs\development\server\proxy-server-v2.yml
2025-08-23 01:51:55.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.c.ProxyServerConfigManager - 配置加载完成 - 服务器端口: 8888, 认证: 启用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
2025-08-23 01:51:55.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.c.ProxyServerConfigManager - 认证配置 - 用户名: admin, 超时时间: 30秒
2025-08-23 01:51:55.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.c.ProxyServerConfigManager - 连接池配置 - 最大连接数/主机: 20, 空闲超时: 60秒, 清理间隔: 30秒
2025-08-23 01:51:55.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.c.ProxyServerConfigManager - 性能监控配置 - 报告间隔: 300秒
2025-08-23 01:51:55.846 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.c.ProxyServerConfigManager - 黑名单配置 - 失败阈值: 3, 缓存超时: 180秒
2025-08-23 01:51:55.924 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.ssl.SslContextManager - 使用配置的密钥库创建SSL上下文: server.p12
2025-08-23 01:51:55.924 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.ssl.SslContextManager - 配置SSL协议: [TLSv1.2, TLSv1.3]
2025-08-23 01:51:55.925 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.ssl.SslContextManager - 禁用客户端认证
2025-08-23 01:51:56.021 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.server.ssl.SslContextManager - SSL上下文创建成功
2025-08-23 01:51:56.021 [ForkJoinPool.commonPool-worker-1] INFO  c.x.p.s.i.AbstractInboundServer - SSL上下文初始化完成: main-multiplex
2025-08-23 01:51:56.168 [main-multiplex-boss] INFO  c.x.p.s.i.AbstractInboundServer - Inbound服务器启动成功: main-multiplex (0.0.0.0:8888)
2025-08-23 01:51:56.169 [main-multiplex-boss] INFO  c.x.p.s.inbound.InboundServerManager - 所有Inbound服务器启动完成
2025-08-23 01:51:56.173 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - Inbound服务器启动完成: ManagerStatistics{total=1, running=1, stopped=0, connections=0}
2025-08-23 01:51:56.173 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 启动监控服务...
2025-08-23 01:51:56.174 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 监控服务启动完成，每300秒输出一次统计信息
2025-08-23 01:51:56.174 [ForkJoinPool.commonPool-worker-1] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动完成
2025-08-23 01:51:56.174 [main] INFO  com.xiang.proxy.server.ProxyServerV2 - 代理服务器V2启动成功，按Ctrl+C停止服务器
