package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.protocol.MultiplexProtocol;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.BiConsumer;

/**
 * 多路复用后端数据处理器
 * 处理从后端服务器返回的数据，并转发给客户端
 */
public class MultiplexBackendDataHandler extends ChannelInboundHandlerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexBackendDataHandler.class);

    private final Channel clientChannel;
    private final int sessionId;
    private final BiConsumer<Integer, String> errorHandler;
    private volatile boolean errorHandled = false; // 防止重复处理错误

    public MultiplexBackendDataHandler(Channel clientChannel, int sessionId,
                                       BiConsumer<Integer, String> errorHandler) {
        this.clientChannel = clientChannel;
        this.sessionId = sessionId;
        this.errorHandler = errorHandler;
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (!(msg instanceof ByteBuf)) {
            logger.warn("收到非ByteBuf消息: {}", msg.getClass());
            return;
        }

        ByteBuf data = (ByteBuf) msg;
        try {
            // 检查数据是否有效
            if (!data.isReadable()) {
                logger.debug("收到空数据: sessionId={}", sessionId);
                return;
            }

            // 检查客户端连接状态
            if (clientChannel == null || !clientChannel.isActive()) {
                logger.warn("客户端连接不可用，丢弃后端数据: sessionId={}, bytes={}",
                        sessionId, data.readableBytes());
                errorHandler.accept(sessionId, "客户端连接不可用");
                return;
            }

            // 零拷贝优化：参考traffic-3.0的TcpDispatchHandler实现
            // 直接使用ByteBuf引用，避免数据拷贝
            MultiplexProtocol.Packet dataPacket = MultiplexProtocol.createZeroCopyDataPacket(sessionId, data, true);
            ByteBuf packetBuffer = dataPacket.encode();

            clientChannel.writeAndFlush(packetBuffer).addListener(future -> {
                if (!future.isSuccess()) {
                    logger.error("转发后端数据到客户端失败: sessionId={}", sessionId, future.cause());
                    errorHandler.accept(sessionId, "转发数据失败: " + future.cause().getMessage());
                } else {
                    logger.debug("转发后端数据到客户端成功: sessionId={}, bytes={}", sessionId, data.readableBytes());
                }
            });

        } catch (Exception e) {
            logger.error("处理后端数据时发生异常: sessionId={}", sessionId, e);
            errorHandler.accept(sessionId, "数据处理异常: " + e.getMessage());
        } finally {
            data.release();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        if (!errorHandled) {
            errorHandled = true;
            logger.debug("后端连接断开: sessionId={}, remote={}", sessionId, ctx.channel().remoteAddress());
            errorHandler.accept(sessionId, "后端连接断开");
        }
        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        if (!errorHandled) {
            errorHandled = true;
            // 根据异常类型提供更具体的错误信息
            String errorMessage;
            if (cause instanceof java.net.ConnectException) {
                errorMessage = "后端连接失败";
            } else if (cause instanceof java.io.IOException && cause.getMessage().contains("Connection reset")) {
                errorMessage = "后端连接异常: Connection reset";
            } else if (cause instanceof java.io.IOException) {
                errorMessage = "后端连接IO异常";
            } else {
                errorMessage = "后端连接异常: " + cause.getMessage();
            }

            logger.warn("后端连接异常: sessionId={}, remote={}, error={}",
                    sessionId, ctx.channel().remoteAddress(), errorMessage);
            errorHandler.accept(sessionId, errorMessage);
        }
        ctx.close();
    }
}
