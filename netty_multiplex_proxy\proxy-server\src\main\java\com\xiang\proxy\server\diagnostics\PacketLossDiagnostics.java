package com.xiang.proxy.server.diagnostics;

import com.xiang.proxy.server.inbound.impl.multiplex.MultiplexBackendDataHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据包丢失诊断工具
 * 用于监控和分析代理服务器中的数据包丢失问题
 */
public class PacketLossDiagnostics {
    private static final Logger logger = LoggerFactory.getLogger(PacketLossDiagnostics.class);
    private static final PacketLossDiagnostics INSTANCE = new PacketLossDiagnostics();
    
    private final ConcurrentHashMap<Integer, SessionStats> sessionStats = new ConcurrentHashMap<>();
    private final ScheduledExecutorService diagnosticsExecutor = Executors.newSingleThreadScheduledExecutor(
            r -> new Thread(r, "PacketLoss-Diagnostics"));
    
    // 全局统计
    private final AtomicLong totalPacketsReceived = new AtomicLong(0);
    private final AtomicLong totalPacketsForwarded = new AtomicLong(0);
    private final AtomicLong totalPacketsDropped = new AtomicLong(0);
    private final AtomicLong totalBytesReceived = new AtomicLong(0);
    private final AtomicLong totalBytesForwarded = new AtomicLong(0);
    
    private volatile boolean diagnosticsEnabled = true;
    
    private PacketLossDiagnostics() {
        // 启动定期诊断任务
        startDiagnosticsTask();
    }
    
    public static PacketLossDiagnostics getInstance() {
        return INSTANCE;
    }
    
    /**
     * 注册会话处理器
     */
    public void registerSession(int sessionId, MultiplexBackendDataHandler handler) {
        if (!diagnosticsEnabled) return;
        
        sessionStats.put(sessionId, new SessionStats(sessionId, handler, System.currentTimeMillis()));
        logger.debug("注册会话诊断: sessionId={}", sessionId);
    }
    
    /**
     * 注销会话处理器
     */
    public void unregisterSession(int sessionId) {
        SessionStats stats = sessionStats.remove(sessionId);
        if (stats != null) {
            logger.debug("注销会话诊断: sessionId={}, 运行时间={}ms", 
                    sessionId, System.currentTimeMillis() - stats.startTime);
        }
    }
    
    /**
     * 记录数据包接收
     */
    public void recordPacketReceived(int sessionId, int bytes) {
        if (!diagnosticsEnabled) return;
        
        totalPacketsReceived.incrementAndGet();
        totalBytesReceived.addAndGet(bytes);
        
        SessionStats stats = sessionStats.get(sessionId);
        if (stats != null) {
            stats.packetsReceived.incrementAndGet();
            stats.bytesReceived.addAndGet(bytes);
        }
    }
    
    /**
     * 记录数据包转发成功
     */
    public void recordPacketForwarded(int sessionId, int bytes) {
        if (!diagnosticsEnabled) return;
        
        totalPacketsForwarded.incrementAndGet();
        totalBytesForwarded.addAndGet(bytes);
        
        SessionStats stats = sessionStats.get(sessionId);
        if (stats != null) {
            stats.packetsForwarded.incrementAndGet();
            stats.bytesForwarded.addAndGet(bytes);
        }
    }
    
    /**
     * 记录数据包丢失
     */
    public void recordPacketDropped(int sessionId, String reason) {
        if (!diagnosticsEnabled) return;
        
        totalPacketsDropped.incrementAndGet();
        
        SessionStats stats = sessionStats.get(sessionId);
        if (stats != null) {
            stats.packetsDropped.incrementAndGet();
            stats.lastDropReason = reason;
            stats.lastDropTime = System.currentTimeMillis();
        }
        
        logger.warn("数据包丢失: sessionId={}, 原因={}", sessionId, reason);
    }
    
    /**
     * 启动诊断任务
     */
    private void startDiagnosticsTask() {
        diagnosticsExecutor.scheduleWithFixedDelay(() -> {
            try {
                performDiagnostics();
            } catch (Exception e) {
                logger.error("诊断任务执行异常", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 执行诊断
     */
    private void performDiagnostics() {
        if (!diagnosticsEnabled || sessionStats.isEmpty()) {
            return;
        }
        
        logger.info("=== 数据包丢失诊断报告 ===");
        logger.info("全局统计: 接收={}, 转发={}, 丢弃={}, 丢包率={:.2f}%",
                totalPacketsReceived.get(), totalPacketsForwarded.get(), totalPacketsDropped.get(),
                calculatePacketLossRate(totalPacketsReceived.get(), totalPacketsDropped.get()));
        
        logger.info("字节统计: 接收={}MB, 转发={}MB",
                totalBytesReceived.get() / 1024.0 / 1024.0,
                totalBytesForwarded.get() / 1024.0 / 1024.0);
        
        // 检查有问题的会话
        int problematicSessions = 0;
        for (SessionStats stats : sessionStats.values()) {
            if (stats.hasPacketLoss()) {
                problematicSessions++;
                logger.warn("会话 {} 存在数据包丢失: 接收={}, 转发={}, 丢弃={}, 丢包率={:.2f}%, 最后丢包原因={}, 运行时间={}ms",
                        stats.sessionId, stats.packetsReceived.get(), stats.packetsForwarded.get(),
                        stats.packetsDropped.get(), stats.getPacketLossRate(), stats.lastDropReason,
                        System.currentTimeMillis() - stats.startTime);
            }
        }
        
        if (problematicSessions > 0) {
            logger.warn("发现 {} 个会话存在数据包丢失问题，总会话数: {}", problematicSessions, sessionStats.size());
        } else {
            logger.info("所有 {} 个会话运行正常，无数据包丢失", sessionStats.size());
        }
    }
    
    /**
     * 计算丢包率
     */
    private double calculatePacketLossRate(long received, long dropped) {
        return received > 0 ? (dropped * 100.0 / received) : 0.0;
    }
    
    /**
     * 获取诊断报告
     */
    public String getDiagnosticsReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 数据包丢失诊断报告 ===\n");
        report.append(String.format("全局统计: 接收=%d, 转发=%d, 丢弃=%d, 丢包率=%.2f%%\n",
                totalPacketsReceived.get(), totalPacketsForwarded.get(), totalPacketsDropped.get(),
                calculatePacketLossRate(totalPacketsReceived.get(), totalPacketsDropped.get())));
        
        report.append(String.format("活跃会话数: %d\n", sessionStats.size()));
        
        int problematicSessions = 0;
        for (SessionStats stats : sessionStats.values()) {
            if (stats.hasPacketLoss()) {
                problematicSessions++;
                report.append(String.format("问题会话 %d: 接收=%d, 转发=%d, 丢弃=%d, 丢包率=%.2f%%\n",
                        stats.sessionId, stats.packetsReceived.get(), stats.packetsForwarded.get(),
                        stats.packetsDropped.get(), stats.getPacketLossRate()));
            }
        }
        
        report.append(String.format("问题会话数: %d\n", problematicSessions));
        return report.toString();
    }
    
    /**
     * 启用/禁用诊断
     */
    public void setDiagnosticsEnabled(boolean enabled) {
        this.diagnosticsEnabled = enabled;
        logger.info("数据包丢失诊断已{}", enabled ? "启用" : "禁用");
    }
    
    /**
     * 清理统计信息
     */
    public void clearStats() {
        sessionStats.clear();
        totalPacketsReceived.set(0);
        totalPacketsForwarded.set(0);
        totalPacketsDropped.set(0);
        totalBytesReceived.set(0);
        totalBytesForwarded.set(0);
        logger.info("诊断统计信息已清理");
    }
    
    /**
     * 关闭诊断服务
     */
    public void shutdown() {
        diagnosticsExecutor.shutdown();
        try {
            if (!diagnosticsExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                diagnosticsExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            diagnosticsExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 会话统计信息
     */
    private static class SessionStats {
        final int sessionId;
        final MultiplexBackendDataHandler handler;
        final long startTime;
        final AtomicLong packetsReceived = new AtomicLong(0);
        final AtomicLong packetsForwarded = new AtomicLong(0);
        final AtomicLong packetsDropped = new AtomicLong(0);
        final AtomicLong bytesReceived = new AtomicLong(0);
        final AtomicLong bytesForwarded = new AtomicLong(0);
        volatile String lastDropReason = "";
        volatile long lastDropTime = 0;
        
        SessionStats(int sessionId, MultiplexBackendDataHandler handler, long startTime) {
            this.sessionId = sessionId;
            this.handler = handler;
            this.startTime = startTime;
        }
        
        boolean hasPacketLoss() {
            return packetsDropped.get() > 0 || 
                   (packetsReceived.get() > packetsForwarded.get() + packetsDropped.get());
        }
        
        double getPacketLossRate() {
            long received = packetsReceived.get();
            return received > 0 ? (packetsDropped.get() * 100.0 / received) : 0.0;
        }
    }
}
